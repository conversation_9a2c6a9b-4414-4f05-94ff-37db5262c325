import { Component, signal, ViewEncapsulation, WritableSignal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SliderComponent } from "../../../../../play-comp-library/src/lib/components/slider/slider.component";

interface SliderDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'awe-app-slider',
  standalone: true,
  imports: [
    CommonModule,
    SliderComponent,
  ],
  templateUrl: './app-slider.component.html',
  styleUrls: ['./app-slider.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppSliderComponent {

  // Documentation sections
  sections: SliderDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple slider with default configuration.',
      showCode: false,
    },
    {
      title: 'Custom Range',
      description: 'Sliders with custom minimum, maximum, and step values.',
      showCode: false,
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'min',
      type: 'number',
      default: '0',
      description: 'Minimum value of the slider.',
    },
    {
      name: 'max',
      type: 'number',
      default: '100',
      description: 'Maximum value of the slider.',
    },
    {
      name: 'value',
      type: 'number',
      default: '0',
      description: 'Current value of the slider.',
    }
  ];

  // Example state management
  basicValue: WritableSignal<number> = signal(50);
  temperatureValue: WritableSignal<number> = signal(22);
  volumeValue: WritableSignal<number> = signal(75);
  progressValue: WritableSignal<number> = signal(35);
  accessibleValue: WritableSignal<number> = signal(65);

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  onSliderChange(value: number): void {
    console.log('Slider value:', value);
  }

  // Specific event handlers for each slider
  onBasicValueChange(value: number): void {
    this.basicValue.set(value);
    console.log('Basic slider value changed:', value);
  }

  onTemperatureChange(value: number): void {
    this.temperatureValue.set(value);
    console.log('Temperature changed:', value + '°C');
  }

  onVolumeChange(value: number): void {
    this.volumeValue.set(value);
    console.log('Volume changed:', value + '%');
  }

  onProgressChange(value: number): void {
    this.progressValue.set(value);
    console.log('Progress changed:', value + '%');
  }

  onAccessibleValueChange(value: number): void {
    this.accessibleValue.set(value);
    console.log('Accessible slider value changed:', value);
  }

  onProgressUpdate(): void {
    const current = this.progressValue();
    if (current < 100) {
      this.progressValue.set(Math.min(100, current + 10));
    } else {
      this.progressValue.set(0);
    }
    console.log('Progress updated to:', this.progressValue());
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `// Basic slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-basic-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="slider-container">
      <ava-slider
        [value]="basicValue()"
        (valueChange)="basicValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class BasicSliderComponent {
  basicValue = signal(50);
}`,

      'custom range': `// Custom range slider example
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-custom-range-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="range-container">
      <ava-slider
        [value]="temperatureValue()"
        [min]="0"
        [max]="40"
        (valueChange)="temperatureValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class CustomRangeSliderComponent {
  temperatureValue = signal(22);
}`,

      'interactive examples': `// Interactive slider examples
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-interactive-sliders',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="interactive-container">
      <ava-slider
        [value]="volumeValue()"
        [min]="0"
        [max]="100"
        (valueChange)="volumeValue.set($event)"
      ></ava-slider>

      <ava-slider
        [value]="progressValue()"
        [min]="0"
        [max]="100"
        (valueChange)="progressValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class InteractiveSliderComponent {
  volumeValue = signal(75);
  progressValue = signal(35);
}`,

      'keyboard navigation': `// Keyboard accessible slider
import { Component, signal } from '@angular/core';
import { SliderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-accessible-slider',
  standalone: true,
  imports: [SliderComponent],
  template: \`
    <div class="accessible-container">
      <ava-slider
        [value]="accessibleValue()"
        [min]="0"
        [max]="100"
        (valueChange)="accessibleValue.set($event)"
      ></ava-slider>
    </div>
  \`
})
export class AccessibleSliderComponent {
  accessibleValue = signal(65);
}`
    };

    return examples[section.toLowerCase()] || '';
  }
}
