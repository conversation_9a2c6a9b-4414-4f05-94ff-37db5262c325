import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, HostListener, Input, Output, ViewChild } from '@angular/core';

@Component({
  selector: 'ava-slider',
  imports: [],
  templateUrl: './slider.component.html',
  styleUrl: './slider.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SliderComponent {
 @Input() min = 0;
  @Input() max = 100;
  @Input() value = 0;
  @Output() valueChange = new EventEmitter<number>();

  @ViewChild('sliderTrack') sliderTrack!: ElementRef;

  isHovered = false;
  isDragging = false;

  private onChange = (value: number) => {};
  private onTouched = () => {};

  get percentage(): number {
    return ((this.value - this.min) / (this.max - this.min)) * 100;
  }

  writeValue(value: number): void {
    this.value = value || 0;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onTrackClick(event: MouseEvent): void {
    this.updateValueFromEvent(event);
  }

  startDrag(event: MouseEvent): void {
    event.preventDefault();
    this.isDragging = true;
    this.isHovered = true;
  }

  onKeyDown(event: KeyboardEvent): void {
    let newValue = this.value;
    
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowUp':
        newValue = this.value + 1;
        break;
      case 'ArrowLeft':
      case 'ArrowDown':
        newValue = this.value - 1;
        break;
      case 'Home':
        newValue = this.min;
        break;
      case 'End':
        newValue = this.max;
        break;
      default:
        return;
    }

    event.preventDefault();
    this.updateValue(newValue);
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (this.isDragging) {
      this.updateValueFromEvent(event);
    }
  }

  @HostListener('document:mouseup')
  onMouseUp(): void {
    if (this.isDragging) {
      this.isDragging = false;
      this.isHovered = false;
      this.onTouched();
    }
  }

  private updateValueFromEvent(event: MouseEvent): void {
    const rect = this.sliderTrack.nativeElement.getBoundingClientRect();
    const percentage = (event.clientX - rect.left) / rect.width;
    const newValue = this.min + percentage * (this.max - this.min);
    this.updateValue(newValue);
  }

  private updateValue(value: number): void {
    const clampedValue = Math.max(this.min, Math.min(this.max, Math.round(value)));
    if (clampedValue !== this.value) {
      this.value = clampedValue;
      this.onChange(this.value);
      this.valueChange.emit(this.value);
    }
  }
}
