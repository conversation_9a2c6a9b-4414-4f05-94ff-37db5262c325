<div class="slider-container">
    <div class="slider" #sliderTrack (mousedown)="onTrackClick($event)">
        <div class="slider-fill" [style.width.%]="percentage"></div>
        <div class="slider-handle" [style.left.%]="percentage" [class.hover]="isHovered" [class.dragging]="isDragging"
            tabindex="0" role="slider" [attr.aria-valuemin]="min" [attr.aria-valuemax]="max"
            [attr.aria-valuenow]="value" (mousedown)="startDrag($event)" (mouseenter)="isHovered = true"
            (mouseleave)="isHovered = false" (keydown)="onKeyDown($event)">
            <div class="handle-ring"></div>
            <div class="handle-core"></div>
            <!-- Movable Tooltip -->
            <div class="slider-tooltip">
                {{value}}%
            </div>
        </div>
    </div>
</div>